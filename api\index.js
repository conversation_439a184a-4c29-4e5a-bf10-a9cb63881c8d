/**
 * 接口信息整理表
 *
 * | 接口地址                   | 功能说明                                   | 特殊逻辑 / 参数说明                                   |
 * |---------------------------|--------------------------------------------|------------------------------------------------------|
	sys/SystemConfig/Get?systemType=WEB_CONFIG&language=zh				获取企业列表
	sys/user/GetModulesByUser?username=HDQ&systemtype=APP				获取菜单
 * | api/sys/user/login        | 实现登录验证，验证通过后返回权限菜单         | 需对比本地缓存的菜单图标，若不存在则更新下载图标       |
 * | api/sys/user/favourite    | 用于 app 收藏菜单相关操作                   | 参数需传入用户名，类型固定为 app                      |
 * | api/sys/user/favouriteSeq | 对 app 收藏菜单的顺序进行调整               | -                                                    |
 * | api/sys/app/heartbeat     | 检查心跳，确认看板是否更新及是否清空缓存页面 | -                                                    |
 * | api/sys/app/server        | 返回服务列表及本机数字 ID                   | -                                                    |
 * | api/sys/app/config        | 返回配置信息（登录页、首页、关于 APP 配置）  | -                                                    |
 * | api/sys/app/version       | 获取 APP web 版本、更新时间、插件 MD5 信息   | -                                                    |
 * | api/sys/app/icon          | 获取 app 图标（上传数组），下载到本地        | 登录后或首次使用时对比图标，触发下载，避免影响性能     |
 * | api/sys/app/notice        | 获取消息                                    | -                                                    |
 * | api/sys/app/updatenoticestatus | 修改消息状态                           | -                                                    |
 * | api/sys/app/kanban        | 与子主题相关功能（需结合实际场景理解）       | -                                                    |
 * | api/sys/app/upgrade       | 应用升级功能，支持 APK/应用两种升级类型      | -                                                    |
 
 */
import {
  axios
} from '@/utils/axios.js'
// 登录验证
export const login = (data, params) => {
  return axios.request({
    method: 'POST',
   // url: 'sys/user/userLogin',
    url: 'app/user/login',
    data,
    params
  })
}

// 返回权限菜单
export const GetModulesByUser = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/user/GetModulesByUser',
    data,
    params // 放到连接后面
  })
}

// 返回权限菜单
export const GetCompanyList = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/SystemConfig/Get',
    data,
    params // 放到连接后面
  })
}

// app 查询-工作台/收藏菜单相关操作
export const favourite = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'app/user/favourite',
    data,
    params
  })
}

// app 查询-工作台/收藏菜单相关操作
export const favouriteAdd = (data, params) => {
  return axios.request({
    method: 'POST',
    url: 'app/user/favourite/add',
    data,
    params
  })
}
// app 查询-工作台/收藏菜单相关操作
export const favouriteDelete = (data, params) => {
  return axios.request({
    method: 'POST',
    url: 'app/user/favourite/delete',
    data,
    params
  })
}

// app 收藏菜单顺序调整
// CurrentId: 当前拖拽项ID  TargetId: 目标项ID Type 移动方向 0上 10下 20 父子移动；DataStruct 0常规 1 树形结构
export const favouriteSeq = (data, params) => {
  return axios.request({
    method: 'POST',
    url: 'app/user/favouriteSeq',
    data,
    params
  })
}

// 心跳检查
export const heartbeat = (data, params) => {
  return axios.request({
    method: 'POST',
    url: 'sys/app/heartbeat',
    data,
    params
  })
}

// 服务列表及本机数字ID
export const server = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/server',
    data,
    params
  })
}

// 获取语言列表
export const getLanguages = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/languages',
    data,
    params
  })
}

// 获取企业列表
export const getEnterprises = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/enterprises',
    data,
    params
  })
}



// 配置信息
export const config = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/config',
    data,
    params
  })
}

// APP版本、更新时间、插件MD5
export const version = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/version',
    data,
    params
  })
}

// 获取app图标并下载到本地
export const icon = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/icon',
    data,
    params
  })
}

// 获取消息
export const notice = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/notice',
    data,
    params
  })
}

// 修改消息状态
export const updateNoticeStatus = (data, params) => {
  return axios.request({
    method: 'POST',
    url: 'sys/app/updatenoticestatus',
    data,
    params
  })
}

// 子主题相关功能
export const kanban = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/kanban',
    data,
    params
  })
}

// 应用升级功能
export const upgrade = (data, params) => {
  return axios.request({
    method: 'GET',
    url: 'sys/app/upgrade',
    data,
    params
  })
}