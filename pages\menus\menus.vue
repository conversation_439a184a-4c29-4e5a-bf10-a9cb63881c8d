<template>

	<view>
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<view class="flex fontSize-34  w-full  justify-center items-center">应用</view>
			<block v-slot:right>
				<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons>
			</block>
		</uni-nav-bar>
		<view :style="{ height: `${windowBodyHeight}px` }" class="flex bg-white w-full">
			<view class="flex justify-between  w-full flex-col">
				<!-- 分类 -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<!-- <uni-easyinput suffixIcon="search" placeholder="搜索应用" @iconClick="iconClick"></uni-easyinput> -->
					<uni-easyinput prefixIcon="search" @change="searchAppClick" @clear="searchAppClick"
						v-model="searchInput" placeholder="搜索应用" @iconClick="searchAppClick"></uni-easyinput>
				</u-sticky>
				<!-- 列表内容 -->
				<scroll-view scroll-y="true" @scroll="onScroll" class="flex-1 overflow-y-auto">
					<view class="  flex  flex-col pl-20 pr-20 ">

						<uni-section class="bg-white" @click="change_Collapse" title="应用列表 (长按可收藏到工作台)" type="line">
							<uni-collapse @change="change_Collapse">
								<uni-collapse-item v-for="(categoryItem, categoryIndex) in menuCategory"
									@longpress1="longpressItem(categoryIndex)" :open="isOpenAllCollapse">
									<block v-slot:title>
										<view style="color: #000000;"
											class="flex ml-20 mt-20 pb-20 fontWeight-bold items-center">
											{{ categoryItem.CMODULE_NAME }}
										</view>
									</block>

									<view class="flex justify-start flex-wrap ">

										<view @click="navigateTo('/pages/common/common', item)"
											@longpress="longpressAppItem(item)"
											style="width: 160rpx;height:160rpx;overflow: hidden; margin-right: 10rpx; margin-bottom:20rpx;"
											v-for="(item, index) in categoryItem.Children" :index="index" :key="index"
											class="flex  relative justify-center flex-col items-center ">
											<!-- {{ {
															"CMODULE_TYPE": "APP",
															"CMODULE_NAME": "设备点检",
															"CMODULE_DESCRIPTION": "设备点检",
															"CID_PATH": "",
															"CNAME_PATH": "",
															"CPARENT_MODULE_ID": "2748682451932059",
															"CSEQUENCE": 0,
															"CBEHAVIOR_PATH": "http:/App/EquipCheck",
															"CBEHAVIOR_CATEGORY": "APP_PLUGIN",
															"CIMAGE_FILE_NAME": "",
															"CIMAGE_FILE_NAME_SUB": "",
															"CIMAGE_ID": "0",
															"CIMAGE_NAME": null,
															"CSTYLE": "",
															"SubModules": [],
															"CMODULE_TYPE_ZH": null,
															"CBEHAVIOR_CATEGORY_ZH": null,
															"CACTION_LIST": [],
															"CPERMISSION_RIGHT": "0",
															"Children": [],
															"IsTop": false,
															"VirtualPath": "1-1-1-1",
															"IS_READONLY": true,
															"IS_UPDATE": false,
															"CID": "2737595490979123",
															"CUSER_CREATED": "SYS",
															"CDATETIME_CREATED": "2022-12-13 09:42:33",
															"CUSER_MODIFIED": "SYS",
															"CDATETIME_MODIFIED": "2022-12-13 09:42:33",
															"CSTATE": "A",
															"CSTATE_BOOL": true,
															"CINSTANCE_ID": "",
															"CROWREMARK": "",
															"CENTERPRISE_CODE": "0",
															"CORG_CODE": "0"
															}  }} -->
											<view class="flex">
												<!-- <span style="font-size: 3em; color: Tomato;">
																	<i class="fas fa-camera"></i>
																  </span> -->
												<!-- {{item}} -->
											
												<template v-if="item.CIMAGE_FILE_NAME">
														<template v-if="item.CIMAGE_FILE_NAME.includes('http')">
															<image :src="item.CIMAGE_FILE_NAME || '/static/images/appICON.png'" class="imageItem"
																mode="aspectFill" />
														</template>
														<template v-else>
																<template v-if="item.CIMAGE_FILE_NAME.startsWith('icon-')">
																	<!-- <svg class="appIcon" aria-hidden="true">
																		<use :xlink:href="'#' + item.CIMAGE_FILE_NAME" class="useIcon"></use>
																	</svg>	 -->
																		<ucs-svg :src="getSvgIconData(item.CIMAGE_FILE_NAME)" :width="35" :height="35" />
																	<!-- <i style="font-size: 32px;" :style="item.CSTYLE" class="fa-solid" :class="[item.CIMAGE_FILE_NAME]"></i> -->
																</template>
																<template v-else>
																		<image :src="item.url || '/static/images/appICON.png'" class="imageItem"
																																		mode="aspectFill" />
																</template>
															
														</template>
													
												</template>
												<template v-else>
													<image :src="item.url || '/static/images/appICON.png'" class="imageItem"
														mode="aspectFill" />
												</template>
											
												<!--  {{item.CIMAGE_FILE_NAME}}{{item.CSTYLE}}-->
													
											</view>

											<view class="flex">
												<text class="textDesc">{{ getAppName(item.CMODULE_NAME) }}</text>
											</view>
											<view v-show="currentLongPressTab == categoryIndex"
												class="absolute top-0 left-0">
												<!-- 收藏 -->
												<checkbox-group>
													<label>
														<checkbox style="transform:scale(0.7)" backgroundColor="#fff"
															borderColor="#f5af1d" activeBorderColor="#f5af1d"
															activeBackgroundColor="#f5af1d" color="#fff" value="cb"
															checked="true" />
														<!-- <text style="margin-left: 10rpx; color: #1d2640;font-size: 24rpx;">收藏</text> -->
													</label>
												</checkbox-group>
											</view>
										</view>
									</view>
								</uni-collapse-item>
							</uni-collapse>
						</uni-section>


					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import * as serviceApi from '@/api/index.js'
	import ucsSvg from '@/uni_modules/ucs-svg/components/ucs-svg/ucs-svg.uvue'
	import {
		onPullDownRefresh
	} from '@dcloudio/uni-app';
	import {
		getCurrentUserInfoByField
	} from '@/utils/tools.js'
	import {
		ref,
		onMounted,
		computed
	} from 'vue'
	import {
		setStorageSync,
		getStorageSync,
		CURRENT_SERVER
	} from '@/utils/Storage.js'

	// 图标数据映射 - 从iconfont.js中提取的常用图标
	const iconDataMap = {
		'icon-a-controlplatform': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M927.68 272L542.08 49.28a59.968 59.968 0 0 0-60.032 0L96.32 272v445.376c0 21.44 11.392 41.216 30.016 51.968L512 992l385.664-222.72a59.968 59.968 0 0 0 30.08-51.904V272zM512 475.008L192.256 290.432 512 105.856l319.68 184.576L512 475.008z m32.064 55.488l319.68-184.576v369.088L544 899.52V530.56z m-64 0v369.024l-319.744-184.512V345.856L480 530.56z"></path></svg>',
		'icon-home': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M384 704v64h256v-64H384z"></path><path d="M489.344 105.344a32 32 0 0 1 45.312 0l416 416-45.248 45.312L832 493.248V864a64 64 0 0 1-64 64H256a64 64 0 0 1-64-64V493.248L118.656 566.656l-45.312-45.312 416-416zM512 173.248l-256 256V864h512V429.248l-256-256z"></path></svg>',
		'icon-setting': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M704 512a192 192 0 1 1-384 0 192 192 0 0 1 384 0z m-64 0a128 128 0 1 0-256 0 128 128 0 0 0 256 0z"></path><path d="M512 80l387.968 216v432L512 944l-387.968-216v-432L512 80zM188.032 333.632v356.736L512 870.72l323.968-180.352V333.632L512 153.28 188.032 333.632z"></path></svg>',
		'icon-search': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M608.64 654.016a304.896 304.896 0 1 1 45.312-45.312l226.048 225.92-45.312 45.376-226.048-225.984z m49.088-237.12a240.832 240.832 0 1 0-481.6 0 240.832 240.832 0 0 0 481.6 0z"></path></svg>',
		'icon-user': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384z m0-64a128 128 0 1 1 0-256 128 128 0 0 1 0 256z"></path><path d="M256 896h512a32 32 0 0 0 32-32v-64a256 256 0 0 0-256-256h-64a256 256 0 0 0-256 256v64a32 32 0 0 0 32 32z m64-96v-32a192 192 0 0 1 192-192h64a192 192 0 0 1 192 192v32H320z"></path></svg>',
		'icon-file': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M223.68 64c-30.4 0-63.68 21.568-63.68 59.008v777.984c0 37.44 33.28 59.008 63.68 59.008h576.64c30.4 0 63.68-21.568 63.68-59.008v-535.68A64 64 0 0 0 845.248 320L608 82.752A64 64 0 0 0 562.752 64H223.68zM544 128v256.832h256V896h-576V128h320z m64 45.248l147.584 147.584H608V173.248z"></path></svg>',
		'icon-folder': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M96 224a64 64 0 0 1 64-64H357.824l8.32 6.08L490.432 256H864a64 64 0 0 1 64 64v512a64 64 0 0 1-64 64h-704a64 64 0 0 1-64-64V224z m241.088 0H160V832h704V320H469.632l-8.384-6.08L337.088 224z"></path></svg>',
		'icon-edit': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M696.448 111.168l207.552 207.552 45.248-45.248L741.76 65.92l-45.248 45.248zM150.528 887.232l231.232-46.208 467.072-467.072L641.216 166.4 174.208 633.408l-46.272 231.232a19.2 19.2 0 0 0 22.592 22.592z m490.688-630.4l117.12 117.12-408.128 408.064-146.304 29.312 29.248-146.368 408.064-408.064z"></path></svg>',
		'icon-delete': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M384 768V384h64v384H384zM576 384v384h64V384H576z"></path><path d="M672 192H896v64h-64v640a64 64 0 0 1-64 64H256a64 64 0 0 1-64-64V256H128V192h224V115.2a51.2 51.2 0 0 1 51.2-51.2h217.6a51.2 51.2 0 0 1 51.2 51.2V192z m-256 0h192V128h-192v64zM256 256v640h512V256H256z"></path></svg>',
		'icon-download': '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M784.896 372.096L544 612.928V32h-64v580.928L239.104 372.096l-45.248 45.248 295.488 295.488a32 32 0 0 0 45.312 0l295.488-295.488-45.248-45.248z"></path><path d="M128 704v128a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64v-128h-64v128H192v-128H128z"></path></svg>'
	};

	// 从iconfont.js中提取SVG图标的函数
	function getSvgIconData(iconName) {
		try {
			// 首先检查本地映射
			if (iconDataMap[iconName]) {
				return iconDataMap[iconName];
			}

			// 尝试从全局变量获取
			let iconfontString = '';

			// 方式1: 通过window对象
			if (typeof window !== 'undefined' && window._iconfont_svg_string_1313822) {
				iconfontString = window._iconfont_svg_string_1313822;
			}
			// 方式2: 通过globalThis对象
			else if (typeof globalThis !== 'undefined' && globalThis._iconfont_svg_string_1313822) {
				iconfontString = globalThis._iconfont_svg_string_1313822;
			}
			// 方式3: 通过global对象
			else if (typeof global !== 'undefined' && global._iconfont_svg_string_1313822) {
				iconfontString = global._iconfont_svg_string_1313822;
			}

			if (iconfontString) {
				// 使用正则表达式匹配指定的symbol
				const symbolRegex = new RegExp(`<symbol id="${iconName}"[^>]*>(.*?)</symbol>`, 's');
				const match = iconfontString.match(symbolRegex);

				if (match) {
					// 提取symbol的viewBox属性
					const viewBoxMatch = match[0].match(/viewBox="([^"]*)"/);
					const viewBox = viewBoxMatch ? viewBoxMatch[1] : '0 0 1024 1024';

					// 构建完整的SVG
					const svgContent = `<svg viewBox="${viewBox}" xmlns="http://www.w3.org/2000/svg">${match[1]}</svg>`;
					return svgContent;
				}
			}

			console.warn(`未找到图标: ${iconName}，使用默认图标`);
			return getDefaultIcon();
		} catch (error) {
			console.error('获取SVG图标数据时出错:', error);
			return getDefaultIcon();
		}
	}

	// 获取默认图标
	function getDefaultIcon() {
		return '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 512m-200 0a200 200 0 1 0 400 0 200 200 0 1 0-400 0Z" fill="#cccccc"></path></svg>';
	}

	const currentUserInfo = ref(null)
	const isOpenAllCollapse = ref(true) // 是否所有折叠
	const navBarHeight = ref(44) // 顶部标题高度
	const currentLongPressTab = ref(null)
	const menuCategory = ref([])
	const searchInput = ref('')
	const copyMenuCategory = ref([])
	const systemInfo = uni.getSystemInfoSync(); //系统信息
	const windowHeight = computed(() => {
		//windowHeight不包含NavigationBar和TabBar的高度
		return systemInfo.windowHeight
	})
	const windowBodyHeight = computed(() => {
		//windowHeight不包含NavigationBar和TabBar的高度
		return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top
	})

	// setTimeout(()=>{
	// 	uni.showTabBarRedDot({
	// 		  index: 0
	// 	})
	// },1000)
	onMounted(() => {
		getAndSetUserInfo()
		checkMenuDataBeforeLoad()

		// 测试图标数据获取
		console.log('测试图标数据:', getSvgIconData('icon-a-controlplatform'));
	})

	// 下拉刷新处理函数
	onPullDownRefresh(async () => {
		console.log('下拉刷新处理函数');
		try {
			// 重新加载数据
			loadMenuList()

			// 停止刷新动画（必须调用）
			setTimeout(function() {
				uni.stopPullDownRefresh({
					// success: function () {
					// 	uni.showToast({
					// 		title:'刷新成功！'
					// 	})
					// },
					// fail: function () {
					// 	uni.showToast({
					// 		title:'刷新失败！'
					// 	})
					// },
					// complete: function () {
					// 	uni.showToast({
					// 		title:'刷新完成！'
					// 	})
					// }
				});
			}, 1000);
		} catch (error) {
			console.error('刷新失败:', error);
			setTimeout(function() {
				// 失败时也要停止动画
				uni.stopPullDownRefresh();
			}, 1000);
		}
	});
	//////////////////////methods//////////////////////////
	function getAppName(appName){
		if(appName && appName.length>5){
			return appName.substring(0, 5)+'...'
		}else{
			return appName
		}
	}
	function checkMenuDataBeforeLoad() {
		let _ModuleData = getStorageSync('ModuleData')
		if (_ModuleData) {
			handleMenuData(_ModuleData)
		} else {
			loadMenuList()
		}
	}

	function getAndSetUserInfo() {
		try {
			let _USER_INFO = getStorageSync('USER_INFO')
			//debugger
			if (_USER_INFO) {
				currentUserInfo.value = _USER_INFO //JSON.parse(_USER_INFO)
			}
		} catch (error) {
			currentUserInfo.value = {}
		}

	}

	function handleMenuData(menuDataList = []) {
		let _menuModulesData = menuDataList.ModuleTrees.filter(item => item.CBEHAVIOR_PATH == 'App')
		if (_menuModulesData && _menuModulesData.length > 0) {
			menuCategory.value = _menuModulesData[0].Children
			copyMenuCategory.value = JSON.parse(JSON.stringify(menuCategory.value))
		}
	}
	const isScrolling = ref(false)
	const scrollTimer = ref(null)

	function onScroll() {
		isScrolling.value = true;
		// 2. 清除之前的计时器（避免多次触发时计时混乱）
		if (scrollTimer.value) {
			clearTimeout(scrollTimer.value);
		}

		// 3. 启动新计时器：300ms 后若没有再滚动，视为滚动结束
		scrollTimer.value = setTimeout(() => {
			isScrolling.value = false; // 标记滚动结束
			scrollTimer.value = null; // 清空计时器
			console.log('isScrolling.value:', isScrolling.value)
		}, 1000);
	}

	// 加载菜单数据
	function loadMenuList() {
		const params = {
			username: getCurrentUserInfoByField('CUSER_NAME'),
			systemtype: 'APP'
		}
		uni.showLoading({
			title: '加载中'
		});
		serviceApi.GetModulesByUser(null, params).then(res => {
			if (res && res.data.code === 200 && res.data.data.Success) {
				setStorageSync('ModuleData', res.data.data.Datas)
				handleMenuData(res.data.data.Datas)
			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '获取菜单信息失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			uni.showToast({
				title: '网络异常，请重试',
				icon: 'none'
			})
		}).finally(() => {
			console.log("finally")
			uni.hideLoading();
		})
	}


	// 应用搜索
	function searchAppClick() {
		let val = searchInput.value
		if (val) {
			let _tempData = JSON.parse(JSON.stringify(copyMenuCategory.value))
			// 过滤的数据是 item.Children 的数据
			menuCategory.value = _tempData.map(item => {
				item.Children = item.Children.filter(child => child.CMODULE_NAME.includes(val))
				return item
			}).filter(item => item.Children.length > 0)

		} else {
			menuCategory.value = JSON.parse(JSON.stringify(copyMenuCategory.value))
		}
	}
	// 保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
	function navigateTo(url, params) {
		if (isLongPress.value) {
			isLongPress.value = false
			return
		}
		console.log("navigateTo===params===", JSON.stringify(params))
		if (!params.text) {
			params.text = params.CMODULE_NAME
		}
		//在起始页面跳转到test.vue页面并传递参数
		uni.navigateTo({
			url: url, //'test?id=1&name=uniapp'
			success: function(res) {
				// 通过eventChannel向被打开页面传送数据
				res.eventChannel.emit('acceptDataFromOpenerPage', params)
			}
		});
	}

	function toggleMenu(type) {
		uni.showActionSheet({
			itemList: ['全部折叠', '全部展开'],
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
				let tabItemIndex = res.tapIndex + 1
				if (tabItemIndex == 1) {
					isOpenAllCollapse.value = false
				} else {
					isOpenAllCollapse.value = true
				}
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}

	function longpressItem(index) {
		console.log('isScrolling.value:', isScrolling.value)
		if (!isScrolling.value) { // 只有不在滚动时，才执行长按逻辑
			currentLongPressTab.value = index
		}
	}
	const isLongPress = ref(false)
	// 搜查app
	function longpressAppItem(item) {
		if (!isScrolling.value) {
			// 只有不在滚动时，才执行长按逻辑
			isLongPress.value = true
			setTimeout(() => {
				isLongPress.value = false
			}, 300) // 300ms 后自动恢复
			let params = {
				CUSER_ID: currentUserInfo.value.CID,
				CMODULE_ID: item.CID,
				CREMARK: item.CMODULE_NAME
			}
			// debugger
			// return
			serviceApi.favouriteAdd(params).then(res => {
				if (res && res.data.code === 200 && res.data.data.Success) {
					uni.showToast({
						title: '收藏成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: res && res.data.data.Content ? res.data.data.Content : '收藏失败',
						icon: 'none'
					})
				}
			}).catch(err => {
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				})
			}).finally(() => {
			
			})
		}
		
	}

	function change_Collapse() {
		currentLongPressTab.value = null
	}
</script>


<style lang="scss">
	.imageItem {
		width: 70rpx;
		height: 70rpx;
	}

	.textDesc {
		font-size: 24rpx;
		margin-top: 10rpx;
		color: #363636; //#8c8c8c;
	}
	.appIcon {
	  width: 70rpx;
	  height: 70rpx; //宽高可设成100% 这样可以根据父级元素改变图标大小
	  fill: currentColor;
	  overflow: hidden;
	}
	
	.useIcon {
	  color: #6b7a99;
	}
</style>