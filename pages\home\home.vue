<template>
	<view class="static bg-color-main">

		<view class="flex absolute" style="width: 100%;z-index: 99999;" :style="{height: `${swiperHeight}px)`}">
			<uni-swiper-dot class="uni-swiper-dot-box static" @clickItem="clickItem" :info="infoList" :current="current"
				:mode="mode" :dots-styles="dotsStyles" field="content">
				<swiper :autoplay="isAutoplay" class="swiper-box" @change="change_swiper" :current="swiperDotIndex">
					<swiper-item v-for="(item, index) in infoList" :key="index">
						<view class="swiper-item" :class="'swiper-item' + index">
							<image  style="width: 100%;height: 100%; background-color: #eeeeee;" mode="aspectFill"
								:src="item.url" @error="imageError"></image>
							<!-- <text style="color: #fff; font-size: 32px;">{{index+1}}</text> -->
						</view>
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
		<view class="flex absolute" style="width: 100%;"
			:style="{height: `calc(100vh - ${swiperHeight}px)`,top:`${swiperHeight}px`}">
			<web-view :webview-styles="webviewStyles" :fullscreen="false" style="height:100%;width:100%"
				:src='webViewUrl'></web-view>
		</view>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		reactive,
		onMounted,
		onUnmounted
	} from 'vue';
	import { setStorageSync, getStorageSync, CURRENT_SERVER } from '@/utils/Storage.js'
	const webViewUrl = ref('https://www.baidu.com')//ref("http://************:5173/#/echarts")
	const navBarHeight = ref(44) // 顶部标题高度https://www.baidu.com
	const tabBarHeight = ref(50) // 底部菜单栏高度
	const swiperHeight = ref(150) //  轮播图高度
	const isAutoplay = ref(true) // 是否自动播放，播放一轮后，设置停止
	const systemInfo = uni.getSystemInfoSync(); //系统信息

	const windowHeight = computed(() => {
		return systemInfo.windowHeight
	})
	const getUserInfo = () => {
		let _userInfo = {}
		try {
			_userInfo = getStorageSync('USER_INFO')
		} catch (e) {
			_userInfo = {}
		}
		return _userInfo
	}
	const getServerData =() =>{
			let _currentServer = {}
			try {
				_currentServer = getStorageSync(CURRENT_SERVER)
				
			} catch (error) {
				_currentServer = {}
			}
			return _currentServer
		}
		const getServerBaseURL= () => {
			//debugger
			let _url = ''
			try {
				let data = getStorageSync(CURRENT_SERVER)
				if (data && data.CMIDDLEWARE) {
					//"CMIDDLEWARE": "http://************:6682/api/"
					_url = data.CMIDDLEWARE
					if(!_url.endsWith('/')){
						_url += '/'
					}
				}
			} catch (e) {
				_url = '/'
			}
			return _url
		}
	//////////////轮播图////////////////////
	const modeIndex = ref(-1)
	const styleIndex = ref(-1)
	const homeConfig = ref(null)
	const current = ref(0)
	const mode = ref('default')
	const dotsStyles = ref({})
	const swiperDotIndex = ref(0)
	const autoPlayTimer = ref(null)
	//////////////轮播图////////////////////
	const webviewStyles = ref({
		top: swiperHeight.value + 'px', //  非自定义页面，不需要顶部菜单栏+安全栏 位置
		width: '100%',
		height: windowHeight.value - swiperHeight.value + 'px',
		progress: {
			color: '#FF3333'
		}
	})
	const infoList = ref([
		{
			colorClass: 'uni-bg-red',
			mode: 'aspectFit',
			url: '/static/images/banner/banner1.png',
			content: '内容 A'
		},
		{
			colorClass: 'uni-bg-green',
			mode: 'aspectFit',
			url: '/static/images/banner/banner2.png',
			content: '内容 B'
		},
		{
			colorClass: 'uni-bg-blue',
			mode: 'aspectFit',
			url: '/static/images/banner/banner3.png',
			content: '内容 C'
		}
	])
	/////////////////VUE 内置生命周期 start///////////////////////
	onMounted(() => {
		// 自动轮播图片功能
		//setAutoPlayIMG()
		setCustomHomePageURL()
		setLoginPageConfig()
	})
	onUnmounted(() => {
		
		//取消自动轮播图片功能
		//clearInterval(autoPlayTimer.value)
	})
	/////////////////VUE 内置生命周期 end///////////////////////
	
	function setLoginPageConfig(){
		//debugger
		let _LOGIN_CONFIG = getStorageSync('LOGIN_CONFIG')
		homeConfig.value={}
		if(_LOGIN_CONFIG ){
			homeConfig.value = JSON.parse(_LOGIN_CONFIG)
			// {
			//   "CarouseList":"/common/images/333141473603653.png;/common/images/333141503995973.png;/common/images/333141533089861.png"
			// }
			let baseURL= 'http://61.144.186.197:19719'
			if(!!homeConfig.value.CarouseList){
				infoList.value =[]
				let img_list = homeConfig.value.CarouseList.split(';')
				if(img_list && img_list.length>0){
					img_list.forEach(item=>{
						infoList.value.push({url:baseURL+item})
					})
				}
			}
							
			
				
		}
	}
	function setCustomHomePageURL(){
		//debugger
		try {
			if(getUserInfo()?.CDEFAULT_APP_HOME_URL){
				let _homeUrl = getUserInfo().CDEFAULT_APP_HOME_URL
				webViewUrl.value = _homeUrl
			}
		} catch (error) {
			//TODO handle the exception
		}
	}
	
	function imageMouseEnter(){
		uni.showToast({
			title:'mouse enter'
		})
		//clearInterval(autoPlayTimer.value)
	}
	function imageMouseLeave(){
		uni.showToast({
			title:'mouse Leave'
		})
		//clearInterval(autoPlayTimer.value)
	}
	function setAutoPlayIMG() {
		//clearInterval(autoPlayTimer.value)
		autoPlayTimer.value = setInterval(() => {
			if (swiperDotIndex.value < 2) {
				swiperDotIndex.value = swiperDotIndex.value + 1
			} else {
				swiperDotIndex.value = 0
			}
		}, 2000)
	}

	function imageError(e) {
		console.error('image发生error事件，携带值为' + e.detail.errMsg)
	}

	function change_swiper(e) {
		current.value = e.detail.current
		if(isAutoplay.value && current.value ==2){
			isAutoplay.value = false
		}
	}

	function clickItem(e) {
		swiperDotIndex.value = e
	}
</script>

<style lang="scss" scoped>
	.vertionDiff {
		/* #ifndef APP */
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 999999;
		/* #endif */
	}

	.swiper-box {
		height: 150px;
	}

	.swiper-item {

		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 150px;
		color: #fff;
	}

	.swiper-item0 {
		background-color: #cee1fd;
	}

	.swiper-item1 {
		background-color: #b2cef7;
	}

	.swiper-item2 {
		background-color: #cee1fd;
	}

	.image {
		width: 750rpx;
	}



	@media screen and (min-width: 500px) {
		.uni-swiper-dot-box {
			width: 400px;
			margin: 0 auto;
			margin-top: 8px;
		}

		.image {
			width: 100%;
		}
	}

	.uni-bg-red {
		background-color: #ff5a5f;
	}

	.uni-bg-green {
		background-color: #09bb07;
	}

	.uni-bg-blue {
		background-color: #007aff;
	}
</style>