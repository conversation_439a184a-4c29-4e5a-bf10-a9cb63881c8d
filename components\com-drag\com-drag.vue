<template>
	
	<movable-area class="u-component u-drag " :style="listStyle">
		<movable-view v-for="(item, index) in newList" :key="item[field]" direction="all" :damping="40" inertia
			class="u-drag-item " :class="{ 'u-drag-item--active': enable && index === moveIndex }" :style="itemStyle"
			:x="item.x" :y="item.y" :disabled=" !enable || item.disabled" @longpress="!isScrolling ||(!item.disabled && setEnable(true))"
			@change="handleChange($event, index)" @touchstart="handleTouchstart(index)" @touchend="handleTouchend()">
			<slot :item="item" :index="index"></slot>
		</movable-view>
	</movable-area>
</template>

<script setup>
	import {
		ref,
		computed,
		nextTick,
		watch
	} from 'vue'
	import pickBy from 'lodash/pickBy'
	import debounce from 'lodash/debounce'
	import sortBy from 'lodash/sortBy'
	import useState from './tools/useState'

	const $emit = defineEmits(['update'])

	const props = defineProps({
		list: {
			type: Array,
			default () {
				return []
			},
		},
		isScrolling:{
			type: Boolean,
			default: false,
		},
		field: {
			type: String,
			default: "CID", // 此字段非常重要，必须要唯一的ID，不然无法正常排序
		},
		column: {
			type: Number,
			default: 1,
		},
		itemHeight: {
			type: Number,
			default: 160,
		},
		listWidth: {
			type: Number,
			default: 700, //380*2
		},
		itemInnerStyle: {
			type: Object,
			default () {
				return {}
			},
		},
	});
	// props end

	const [enable, setEnable] = useState(false)
	const [moveIndex, setMoveIndex] = useState(0)
	const [moveSortIndex, setMoveSortIndex] = useState(0)
	const [moveToSortIndex, setMoveToSortIndex] = useState(0)
	const [originalSourceItem, setOriginalSourceItem] = useState(null) // 保存原始被拖拽的item
	const [originalTargetItem, setOriginalTargetItem] = useState(null) // 保存目标位置的原始item
	const newList = ref(props.list)

	const listStyle = computed(() =>
		pickBy({
			width: props.listWidth + 'rpx',
			height: props.itemHeight * Math.ceil(props.list.length / props.column) + 'rpx'
		})
	)
	const itemStyle = computed(() =>
		pickBy({
			...props.itemInnerStyle,
			width: props.listWidth / props.column + 'rpx',
			height: props.itemHeight + 'rpx'
		})
	)

	// index: number
	const getX = (index) => {
		return (index % props.column) * (props.listWidth / props.column)
	}
	//index: number
	const getY = (index) => {
		return Math.floor(index / props.column) * props.itemHeight
	}

	const initList = () => {
		newList.value.map(item => {
			item.x = getX(item.sortIndex) + 'rpx'
			item.y = getY(item.sortIndex) + 'rpx'
		})
	}
	//index: number
	const handleTouchstart = (index) => {
		if(props.isScrolling){
			return
		}
		console.log('handleTouchstart：',index)
		setMoveIndex(index)
		setMoveSortIndex(newList.value[index].sortIndex)
		setMoveToSortIndex(newList.value[index].sortIndex) // 初始化为相同值
		// 保存原始被拖拽的item信息（深拷贝）
		setOriginalSourceItem(JSON.parse(JSON.stringify(newList.value[index])))
		// 重置目标item
		setOriginalTargetItem(null)
	}

	//function (e: any, index: number
	const handleChange = debounce(function(e, index) {
		if(props.isScrolling){
			return
		}
		if (e.detail.source === 'touch') {
			setMoveIndex(index)
			// 注意：不要在这里重新设置moveSortIndex，因为它应该保持为拖拽开始时的原始值

			newList.value[index].x = e.detail.x
			newList.value[index].y = e.detail.y

			const itemWidth = props.listWidth / props.column

			const maxArea = ((itemWidth / 2) * (props.itemHeight / 2)) / 2

			for (let i = 0; i < newList.value.length; i++) {
				const item = newList.value[i]
				const targetX = getX(item.sortIndex)
				const targetY = getY(item.sortIndex)
				if (
					Math.abs(targetX / 2 - e.detail.x) > itemWidth / 2 ||
					Math.abs(targetY / 2 - e.detail.y) > props.itemHeight / 2 ||
					item.disabled
				) {
					continue
				} else if (
					(itemWidth / 2 - Math.abs(targetX / 2 - e.detail.x)) *
					(props.itemHeight / 2 - Math.abs(targetY / 2 - e.detail.y)) >
					maxArea
				) {
					// 两个item 相交面积大于item面积的一半
					setMoveToSortIndex(item.sortIndex)
					// 保存目标位置的原始item信息（在sortIndex被修改之前）
					setOriginalTargetItem(JSON.parse(JSON.stringify(item)))
					console.log('toSortIndex', item.sortIndex)

					if (moveSortIndex.value > moveToSortIndex.value) {
						newList.value.map(item => {
							if (item.sortIndex === moveSortIndex.value) {
								item.sortIndex = moveToSortIndex.value
							} else if (
								item.sortIndex >= moveToSortIndex.value &&
								item.sortIndex < moveSortIndex.value
							) {
								item.sortIndex++
								item.x = getX(item.sortIndex) + 'rpx'
								item.y = getY(item.sortIndex) + 'rpx'
							}
						})
					} else {
						newList.value.map(item => {
							if (item.sortIndex === moveSortIndex.value) {
								item.sortIndex = moveToSortIndex.value
							} else if (
								item.sortIndex <= moveToSortIndex.value &&
								item.sortIndex > moveSortIndex.value
							) {
								item.sortIndex--
								item.x = getX(item.sortIndex) + 'rpx'
								item.y = getY(item.sortIndex) + 'rpx'
							}
						})
					}

					break
				}
			}
		}
	}, 80)

	const handleTouchend = () => {
		if(props.isScrolling){
			return
		}
		if (enable.value) {
			console.log('=====handleTouchend====')
			nextTick(() => {
				initList()
			})

			// 根据实际需要，此处可自定义
			let newData = [...newList.value].sort(function(a, b) {
				    //console.log('handleTouchend a and b:',a,b)
					return a.sortIndex - b.sortIndex
				})

			// 检查是否真的发生了位置移动
			if (moveSortIndex.value !== moveToSortIndex.value && originalTargetItem.value) {
				// 获取移动源item和目标item
				// 移动源item：被拖拽的item（使用保存的原始信息）
				const sourceItem = originalSourceItem.value

				// 目标item：目标位置的原始item（拖拽发生前位于目标位置的item）
				const targetItem = originalTargetItem.value

				const sourceTarget = {
					sourceItem: sourceItem, // 拖拽的那个item（移动源）- 原始状态
					targetItem: targetItem, // 目标位置的原始item（目标item）- 原始状态
					originalSourceIndex: moveSortIndex.value, // 原始位置索引
					newTargetIndex: moveToSortIndex.value, // 新位置索引
					moveIndex: moveIndex.value, // 被拖拽item在数组中的索引
					hasMoved: true // 标识确实发生了移动
				}

				console.log('移动源item:', sourceItem)
				console.log('目标item:', targetItem)
				console.log('sourceTarget:', sourceTarget)

				$emit('update', newData, sourceTarget)
				
			} else {
				// 没有发生位置移动，只返回新数据
				console.log('没有发生位置移动')
									$emit('update', newData, {
										sourceItem: originalSourceItem.value,
										targetItem: null,
										originalSourceIndex: moveSortIndex.value,
										newTargetIndex: moveSortIndex.value,
										moveIndex: moveIndex.value,
										hasMoved: false
									})
				
			}
			setEnable(false)
		}
	}

	watch(
		() => props.list,
		() => {
			newList.value = props.list.map((item, index) => {
				return {
					...item,
					index,
					sortIndex: index
				}
			})

			initList()
		}, {
			immediate: true,
			deep: true
		}
	)
</script>

<style lang="scss">
	.u-drag {
		position: relative;

		&-item {
			&--active {
				z-index: 9;
				background: #e9e9e9;
				border: 1px dashed #ccc;
				box-shadow: 0 4rpx 16rpx 4rpx #ccc;
			}
		}
	}
</style>