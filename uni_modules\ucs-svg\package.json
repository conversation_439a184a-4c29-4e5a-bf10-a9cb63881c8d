{"id": "ucs-svg", "displayName": "ucs-svg 矢量图形", "version": "1.0.5", "description": "SVG 矢量图形组件是用于渲染和操作 SVG 资源", "keywords": ["svg,矢量图形"], "repository": "https://gitee.com/cloudsimpler/uni-ucs-design/tree/master/uni_modules/ucs-svg", "engines": {"HBuilderX": "^4.24", "uni-app": "^4.66", "uni-app-x": "^4.66"}, "dcloudext": {"type": "uts-vue-component", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "1827239383"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "x", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "x", "android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "√", "qq": "√", "lark": "√"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "21"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}